@echo off
echo 正在设置SSH配置...

REM 创建.ssh目录（如果不存在）
if not exist "%USERPROFILE%\.ssh" (
    mkdir "%USERPROFILE%\.ssh"
    echo 已创建 .ssh 目录
)

REM 创建或追加SSH配置
echo.
echo Host tank-server >> "%USERPROFILE%\.ssh\config"
echo     HostName ************** >> "%USERPROFILE%\.ssh\config"
echo     User Administrator >> "%USERPROFILE%\.ssh\config"
echo     Port 22 >> "%USERPROFILE%\.ssh\config"
echo     ServerAliveInterval 60 >> "%USERPROFILE%\.ssh\config"
echo     ServerAliveCountMax 3 >> "%USERPROFILE%\.ssh\config"
echo.

echo SSH配置已添加到 %USERPROFILE%\.ssh\config
echo.
echo 配置内容：
type "%USERPROFILE%\.ssh\config"
echo.
echo 现在您可以在VSCode中连接到 tank-server
pause
